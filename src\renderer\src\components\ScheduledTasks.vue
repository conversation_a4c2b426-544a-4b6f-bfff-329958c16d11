<template>
  <div class="scheduled-tasks-container">
    <div class="header">
      <h2>定时任务管理</h2>
      <p>创建和管理定时执行的脚本任务</p>
    </div>
    
    <div class="tasks-content">
      <div class="tasks-list-panel">
        <div class="panel-header">
          <h3>任务列表</h3>
          <button @click="openAddModal" class="add-btn">新增任务</button>
        </div>
        
        <div class="tasks-list">
          <div v-if="tasks.length === 0" class="empty-state">
            <div class="empty-icon">⏰</div>
            <p>暂无定时任务</p>
            <span>点击"新增任务"创建您的第一个定时任务</span>
          </div>
          
          <div v-else>
            <div
              v-for="task in tasks"
              :key="task.id"
              class="task-item"
              :class="{ active: selectedTask && selectedTask.id === task.id }"
              @click="selectTask(task)"
            >
              <div class="task-info">
                <div class="task-name">{{ task.name }}</div>
                <div class="task-schedule">{{ task.schedule }}</div>
                <div class="task-script">{{ task.scriptType }}: {{ task.scriptName }}</div>
              </div>
              <div class="task-status">
                <span class="status-indicator" :class="task.enabled ? 'enabled' : 'disabled'">
                  {{ task.enabled ? '启用' : '禁用' }}
                </span>
                <button @click.stop="toggleTask(task)" class="toggle-btn">
                  {{ task.enabled ? '禁用' : '启用' }}
                </button>
                <button @click.stop="deleteTask(task)" class="delete-btn">删除</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="task-detail-panel" v-if="selectedTask">
        <div class="detail-header">
          <h3>任务详情</h3>
          <div class="detail-actions">
            <button @click="runTaskNow" class="run-now-btn">立即执行</button>
            <button @click="editTask" class="edit-btn">编辑</button>
          </div>
        </div>
        
        <div class="detail-content">
          <div class="detail-section">
            <label>任务名称</label>
            <div class="detail-value">{{ selectedTask.name }}</div>
          </div>
          
          <div class="detail-section">
            <label>执行计划</label>
            <div class="detail-value">{{ selectedTask.schedule }}</div>
          </div>
          
          <div class="detail-section">
            <label>脚本类型</label>
            <div class="detail-value">{{ selectedTask.scriptType }}</div>
          </div>
          
          <div class="detail-section">
            <label>脚本名称</label>
            <div class="detail-value">{{ selectedTask.scriptName }}</div>
          </div>
          
          <div class="detail-section">
            <label>下次执行时间</label>
            <div class="detail-value">{{ getNextRunTime(selectedTask) }}</div>
          </div>
          
          <div class="detail-section">
            <label>最后执行时间</label>
            <div class="detail-value">{{ selectedTask.lastRun || '从未执行' }}</div>
          </div>
          
          <div class="detail-section">
            <label>执行状态</label>
            <div class="detail-value">
              <span class="status-indicator" :class="selectedTask.enabled ? 'enabled' : 'disabled'">
                {{ selectedTask.enabled ? '启用' : '禁用' }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="task-detail-panel placeholder">
        <div class="placeholder-content">
          <div class="placeholder-icon">📋</div>
          <p>选择一个任务查看详情</p>
        </div>
      </div>
    </div>
    
    <!-- 新增任务模态框 -->
    <div v-if="showAddModal" class="modal-overlay" @click.self="closeAddModal">
      <div class="modal-content">
        <h3>新增定时任务</h3>
        
        <div class="form-group">
          <label>任务名称</label>
          <input type="text" v-model="newTask.name" placeholder="请输入任务名称" />
        </div>
        
        <div class="form-group">
          <label>脚本类型</label>
          <select v-model="newTask.scriptType">
            <option value="">请选择脚本类型</option>
            <option value="CMD">CMD脚本</option>
            <option value="JS">JS脚本</option>
            <option value="Python">Python脚本</option>
          </select>
        </div>
        
        <div class="form-group">
          <label>脚本名称</label>
          <input type="text" v-model="newTask.scriptName" placeholder="请输入脚本名称" />
        </div>
        
        <div class="form-group">
          <label>执行计划</label>
          <select v-model="newTask.scheduleType">
            <option value="interval">间隔执行</option>
            <option value="cron">Cron表达式</option>
          </select>
        </div>
        
        <div v-if="newTask.scheduleType === 'interval'" class="form-group">
          <label>执行间隔</label>
          <div class="interval-inputs">
            <input type="number" v-model="newTask.intervalValue" min="1" />
            <select v-model="newTask.intervalUnit">
              <option value="minutes">分钟</option>
              <option value="hours">小时</option>
              <option value="days">天</option>
            </select>
          </div>
        </div>
        
        <div v-if="newTask.scheduleType === 'cron'" class="form-group">
          <label>Cron表达式</label>
          <input type="text" v-model="newTask.cronExpression" placeholder="例如: 0 0 * * *" />
          <small>格式: 分 时 日 月 周</small>
        </div>
        
        <div class="modal-actions">
          <button @click="closeAddModal">取消</button>
          <button @click="addTask" :disabled="!isFormValid">确认添加</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const tasks = ref([])
const selectedTask = ref(null)
const showAddModal = ref(false)

const newTask = ref({
  name: '',
  scriptType: '',
  scriptName: '',
  scheduleType: 'interval',
  intervalValue: 1,
  intervalUnit: 'hours',
  cronExpression: '',
  enabled: true
})

const isFormValid = computed(() => {
  return newTask.value.name && 
         newTask.value.scriptType && 
         newTask.value.scriptName &&
         (newTask.value.scheduleType === 'interval' || newTask.value.cronExpression)
})

function openAddModal() {
  showAddModal.value = true
}

function closeAddModal() {
  showAddModal.value = false
  resetNewTask()
}

function resetNewTask() {
  newTask.value = {
    name: '',
    scriptType: '',
    scriptName: '',
    scheduleType: 'interval',
    intervalValue: 1,
    intervalUnit: 'hours',
    cronExpression: '',
    enabled: true
  }
}

function addTask() {
  const task = {
    id: Date.now(),
    ...newTask.value,
    schedule: getScheduleText(newTask.value),
    lastRun: null,
    createdAt: new Date().toISOString()
  }
  
  tasks.value.push(task)
  closeAddModal()
}

function getScheduleText(task) {
  if (task.scheduleType === 'interval') {
    const unitText = {
      minutes: '分钟',
      hours: '小时',
      days: '天'
    }
    return `每${task.intervalValue}${unitText[task.intervalUnit]}`
  } else {
    return task.cronExpression
  }
}

function selectTask(task) {
  selectedTask.value = task
}

function toggleTask(task) {
  task.enabled = !task.enabled
}

function deleteTask(task) {
  const index = tasks.value.findIndex(t => t.id === task.id)
  if (index > -1) {
    tasks.value.splice(index, 1)
    if (selectedTask.value && selectedTask.value.id === task.id) {
      selectedTask.value = null
    }
  }
}

function runTaskNow() {
  if (selectedTask.value) {
    console.log('立即执行任务:', selectedTask.value.name)
    // 这里可以添加实际的任务执行逻辑
  }
}

function editTask() {
  if (selectedTask.value) {
    console.log('编辑任务:', selectedTask.value.name)
    // 这里可以添加编辑任务的逻辑
  }
}

function getNextRunTime() {
  // 这里应该根据任务的调度计划计算下次执行时间
  // 简化实现，返回一个示例时间
  return '2024-07-11 18:00:00'
}
</script>

<style scoped>
.scheduled-tasks-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #1a1a1a;
  color: #e0e0e0;
  overflow: hidden;
}

.header {
  padding: 24px;
  border-bottom: 1px solid #333;
}

.header h2 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #ffffff;
}

.header p {
  font-size: 14px;
  color: #b0b0b0;
  margin: 0;
}

.tasks-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.tasks-list-panel {
  width: 350px;
  border-right: 1px solid #333;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #ffffff;
}

.add-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.add-btn:hover {
  background-color: #45a049;
}

.tasks-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: #888;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 8px;
  color: #b0b0b0;
}

.empty-state span {
  font-size: 14px;
  color: #888;
}

.task-item {
  background-color: #252525;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  cursor: pointer;
  border: 1px solid #333;
  transition: all 0.2s ease;
}

.task-item:hover {
  background-color: #2c2c2c;
  border-color: #444;
}

.task-item.active {
  background-color: #1e3a5f;
  border-color: #4a90e2;
}

.task-info {
  margin-bottom: 12px;
}

.task-name {
  font-size: 15px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 4px;
}

.task-schedule {
  font-size: 13px;
  color: #4a90e2;
  margin-bottom: 4px;
}

.task-script {
  font-size: 12px;
  color: #888;
}

.task-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.status-indicator.enabled {
  background-color: #4caf50;
  color: white;
}

.status-indicator.disabled {
  background-color: #666;
  color: #ccc;
}

.toggle-btn, .delete-btn {
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.toggle-btn {
  background-color: #666;
  color: white;
}

.toggle-btn:hover {
  background-color: #777;
}

.delete-btn {
  background-color: #f44336;
  color: white;
}

.delete-btn:hover {
  background-color: #d32f2f;
}

.task-detail-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.task-detail-panel.placeholder {
  justify-content: center;
  align-items: center;
}

.placeholder-content {
  text-align: center;
  color: #888;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.placeholder-content p {
  font-size: 16px;
  margin: 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #333;
}

.detail-header h3 {
  margin: 0;
  font-size: 16px;
  color: #ffffff;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.run-now-btn, .edit-btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.run-now-btn {
  background-color: #ff9800;
  color: white;
}

.run-now-btn:hover {
  background-color: #f57c00;
}

.edit-btn {
  background-color: #2196f3;
  color: white;
}

.edit-btn:hover {
  background-color: #1976d2;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section label {
  display: block;
  font-size: 13px;
  color: #888;
  margin-bottom: 6px;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #e0e0e0;
  padding: 8px 12px;
  background-color: #252525;
  border-radius: 6px;
  border: 1px solid #333;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #1a1a1a;
  border-radius: 12px;
  padding: 24px;
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid #333;
}

.modal-content h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #ffffff;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 13px;
  color: #b0b0b0;
  margin-bottom: 6px;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border-radius: 6px;
  border: 1px solid #444;
  background-color: #252525;
  color: #e0e0e0;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4a90e2;
}

.interval-inputs {
  display: flex;
  gap: 8px;
}

.interval-inputs input {
  flex: 1;
}

.interval-inputs select {
  flex: 1;
}

.form-group small {
  display: block;
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #333;
}

.modal-actions button {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.modal-actions button:first-child {
  background-color: #666;
  color: white;
}

.modal-actions button:first-child:hover {
  background-color: #777;
}

.modal-actions button:last-child {
  background-color: #4caf50;
  color: white;
}

.modal-actions button:last-child:hover:not(:disabled) {
  background-color: #45a049;
}

.modal-actions button:disabled {
  background-color: #444;
  color: #888;
  cursor: not-allowed;
}
</style>
