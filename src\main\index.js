import { app, shell, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'

// --- Database Setup ---
import path from 'path'
import sqlite3 from 'sqlite3'
import { open } from 'sqlite'
import fs from 'fs'
import { exec, spawn } from 'child_process'
import iconv from 'iconv-lite'

// 在开发环境中使用项目根目录，在生产环境中使用app路径
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged
const appRoot = isDev ? process.cwd() : app.getAppPath()
const scriptsRoot = join(appRoot, 'scripts')
const pythonScriptsPath = join(scriptsRoot, 'python')
const jsScriptsPath = join(scriptsRoot, 'js')

let db

async function initializeDatabase() {
  const dbPath = path.join(app.getAppPath(), 'database.db')
  console.log('Database path:', dbPath)

  db = await open({
    filename: dbPath,
    driver: sqlite3.Database
  })

  await db.exec(`
    CREATE TABLE IF NOT EXISTS cmd_scripts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      content TEXT
    );
  `)

  // Table to store python script configurations
  await db.exec(`
    CREATE TABLE IF NOT EXISTS python_script_configs (
      script_name TEXT PRIMARY KEY,
      config_schema TEXT,
      config_values TEXT
    );
  `)

  // Table to store js script configurations
  await db.exec(`
    CREATE TABLE IF NOT EXISTS js_script_configs (
      script_name TEXT PRIMARY KEY,
      config_schema TEXT,
      config_values TEXT
    );
  `)

  await db.exec(`
    CREATE TABLE IF NOT EXISTS script_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      script_id INTEGER,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      log_content TEXT,
      FOREIGN KEY (script_id) REFERENCES cmd_scripts (id) ON DELETE CASCADE
    );
  `)
}

// --- IPC Handlers ---

// Test handler for debugging
ipcMain.handle('test:python-environment', async () => {
  console.log('=== Test Python Environment Called ===')

  const { exec } = require('child_process')
  const util = require('util')
  const execAsync = util.promisify(exec)

  try {
    const result = await execAsync('python --version')
    console.log('Python version check result:', result)
    return createSerializableObject({
      success: true,
      version: result.stdout.trim() || result.stderr.trim()
    })
  } catch (error) {
    console.log('Python version check error:', error)
    return createSerializableObject({
      success: false,
      error: error.message
    })
  }
})

// Test handler for Node environment
ipcMain.handle('test:node-environment', async () => {
  console.log('=== Test Node Environment Called ===')

  const { exec } = require('child_process')
  const util = require('util')
  const execAsync = util.promisify(exec)

  try {
    const result = await execAsync('node --version')
    console.log('Node version check result:', result)
    return createSerializableObject({
      success: true,
      version: result.stdout.trim() || result.stderr.trim()
    })
  } catch (error) {
    console.log('Node version check error:', error)
    return createSerializableObject({
      success: false,
      error: error.message
    })
  }
})

// CMD Scripts CRUD
ipcMain.handle('db:get-cmd-scripts', async () => {
  return await db.all('SELECT * FROM cmd_scripts ORDER BY name')
})

ipcMain.handle('db:add-cmd-script', async (event, { name, description }) => {
  const result = await db.run(
    'INSERT INTO cmd_scripts (name, description, content) VALUES (?, ?, ?)',
    name,
    description,
    `@echo off\nrem New script for ${name}`
  )
  return { id: result.lastID, name, description, content: '' }
})

ipcMain.handle('db:update-cmd-script', async (event, { id, content }) => {
  return await db.run('UPDATE cmd_scripts SET content = ? WHERE id = ?', content, id)
})

ipcMain.handle('db:delete-cmd-script', async (event, id) => {
  return await db.run('DELETE FROM cmd_scripts WHERE id = ?', id)
})

// --- Python Scripts: Hybrid Filesystem & DB ---

// Scan filesystem for script directories
ipcMain.handle('fs:get-python-scripts', async () => {
  if (!fs.existsSync(pythonScriptsPath)) {
    return []
  }
  const dirents = fs.readdirSync(pythonScriptsPath, { withFileTypes: true })
  return dirents
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => ({
      name: dirent.name,
      path: join(pythonScriptsPath, dirent.name)
    }))
})

// Get a script's configuration from the database
ipcMain.handle('db:get-python-script-config', async (event, scriptName) => {
  let row = await db.get(
    'SELECT * FROM python_script_configs WHERE script_name = ?',
    scriptName
  )

  // If script config is not in DB, try to load from file and create it (self-healing)
  if (!row) {
    const scriptConfigPath = join(pythonScriptsPath, scriptName, 'config.json')
    if (fs.existsSync(scriptConfigPath)) {
      try {
        const schemaContent = fs.readFileSync(scriptConfigPath, 'utf-8')
        const schema = JSON.parse(schemaContent)

        // Generate default values from the schema
        const values = {}
        schema.forEach((field) => {
          if (field.default !== undefined) {
            values[field.name] = field.default
          } else {
            // Provide a sensible fallback default
            switch (field.type) {
              case 'text':
                values[field.name] = ''
                break
              case 'number':
                values[field.name] = 0
                break
              case 'checkbox':
                values[field.name] = false
                break
              case 'select':
                values[field.name] = field.options?.[0] || ''
                break
              default:
                values[field.name] = null
            }
          }
        })

        // Insert into DB
        await db.run(
          'INSERT INTO python_script_configs (script_name, config_schema, config_values) VALUES (?, ?, ?)',
          scriptName,
          JSON.stringify(schema),
          JSON.stringify(values)
        )

        // Re-fetch the row to ensure consistency
        row = await db.get('SELECT * FROM python_script_configs WHERE script_name = ?', scriptName)
      } catch (e) {
        console.error(`Error loading or parsing config.json for ${scriptName}:`, e)
        return null // Failed to process, so no config available
      }
    }
  }

  if (row) {
    return {
      script_name: row.script_name,
      schema: JSON.parse(row.config_schema || '[]'),
      values: JSON.parse(row.config_values || '{}')
    }
  }

  return null // No DB entry and no config.json file found
})

// Update a script's configuration in the database
ipcMain.handle('db:update-python-script-config', async (event, { scriptName, values, schema }) => {
  if (schema) {
    // 如果提供了schema，同时更新schema和values
    return await db.run(
      'UPDATE python_script_configs SET config_schema = ?, config_values = ? WHERE script_name = ?',
      JSON.stringify(schema),
      JSON.stringify(values),
      scriptName
    )
  } else {
    // 只更新values
    return await db.run(
      'UPDATE python_script_configs SET config_values = ? WHERE script_name = ?',
      JSON.stringify(values),
      scriptName
    )
  }
})

// Add a new script (creates directory and DB entry)
ipcMain.handle('fs:add-python-script', async (event, name) => {
  try {
    const newScriptPath = join(pythonScriptsPath, name)
    if (fs.existsSync(newScriptPath)) {
      throw new Error(`脚本目录 '${name}' 已存在。`)
    }
    fs.mkdirSync(newScriptPath, { recursive: true })
    
    // Create a default main.py file
    const defaultContent = `# Script for ${name}
import sys
import json
import base64

def main(config):
    """
    Main execution function.
    'config' is a dictionary with your script's parameters.
    """
    script_name = "${name}"
    print(f"--- Running script '{script_name}' ---")
    print("Received configuration:")
    print(json.dumps(config, indent=2, ensure_ascii=False))

    # --- Your script logic starts here ---
    # Example: Accessing a config value
    if config.get('enable_logging'):
        print("\\nLogging is enabled.")
    # --- Your script logic ends here ---

    print("\\n--- Script finished ---")

if __name__ == "__main__":
    # The Base64 encoded config is passed as the first and only argument
    if len(sys.argv) > 1:
        try:
            config_base64 = sys.argv[1]
            config_json_bytes = base64.b64decode(config_base64)
            config_json = config_json_bytes.decode('utf-8')
            config_data = json.loads(config_json)
            main(config_data)
        except Exception as e:
            print(f"Error decoding or parsing config from Base64: {e}", file=sys.stderr)
            sys.exit(1)
    else:
        # Fallback for direct execution without arguments, for testing
        print("Running with default empty config (no arguments provided).")
        main({})
`
    fs.writeFileSync(join(newScriptPath, 'main.py'), defaultContent)

    // For a new script, we start with an empty configuration.
    // The user will add fields via the UI.
    const emptySchema = []
    const emptyValues = {}

    // Create an empty config.json as a placeholder and for consistency with the self-healing mechanism.
    fs.writeFileSync(join(newScriptPath, 'config.json'), JSON.stringify(emptySchema, null, 2))

    // Add entry to the database with an empty config
    await db.run(
      'INSERT OR IGNORE INTO python_script_configs (script_name, config_schema, config_values) VALUES (?, ?, ?)',
      name,
      JSON.stringify(emptySchema),
      JSON.stringify(emptyValues)
    )

    return { name, path: newScriptPath }
  } catch (error) {
    throw { message: error.message }
  }
})

// Delete a script (deletes directory and DB entry)
ipcMain.handle('fs:delete-python-script', async (event, name) => {
  try {
    const scriptPath = join(pythonScriptsPath, name)
    if (!fs.existsSync(scriptPath)) {
      throw new Error(`脚本目录 '${name}' 未找到。`)
    }
    fs.rmSync(scriptPath, { recursive: true, force: true })
    
    // Also delete from the database
    await db.run('DELETE FROM python_script_configs WHERE script_name = ?', name)

    return { success: true }
  } catch (error) {
    throw { message: error.message }
  }
})

// --- JS Scripts: Hybrid Filesystem & DB ---

// Scan filesystem for JS script directories
ipcMain.handle('fs:get-js-scripts', async () => {
  if (!fs.existsSync(jsScriptsPath)) {
    return []
  }
  const dirents = fs.readdirSync(jsScriptsPath, { withFileTypes: true })
  return dirents
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => ({
      name: dirent.name,
      path: join(jsScriptsPath, dirent.name)
    }))
})

// Get a JS script's configuration from the database
ipcMain.handle('db:get-js-script-config', async (event, scriptName) => {
  let row = await db.get(
    'SELECT * FROM js_script_configs WHERE script_name = ?',
    scriptName
  )

  // If script config is not in DB, try to load from file and create it (self-healing)
  if (!row) {
    const scriptConfigPath = join(jsScriptsPath, scriptName, 'config.json')
    if (fs.existsSync(scriptConfigPath)) {
      try {
        const schemaContent = fs.readFileSync(scriptConfigPath, 'utf-8')
        const schema = JSON.parse(schemaContent)

        // Generate default values from the schema
        const values = {}
        schema.forEach((field) => {
          if (field.default !== undefined) {
            values[field.name] = field.default
          } else {
            // Provide a sensible fallback default
            switch (field.type) {
              case 'text':
                values[field.name] = ''
                break
              case 'number':
                values[field.name] = 0
                break
              case 'checkbox':
                values[field.name] = false
                break
              case 'select':
                values[field.name] = field.options?.[0] || ''
                break
              default:
                values[field.name] = null
            }
          }
        })

        // Insert into DB
        await db.run(
          'INSERT INTO js_script_configs (script_name, config_schema, config_values) VALUES (?, ?, ?)',
          scriptName,
          JSON.stringify(schema),
          JSON.stringify(values)
        )

        // Re-fetch the row to ensure consistency
        row = await db.get('SELECT * FROM js_script_configs WHERE script_name = ?', scriptName)
      } catch (e) {
        console.error(`Error loading or parsing config.json for ${scriptName}:`, e)
        return null // Failed to process, so no config available
      }
    }
  }

  if (row) {
    return {
      script_name: row.script_name,
      schema: JSON.parse(row.config_schema || '[]'),
      values: JSON.parse(row.config_values || '{}')
    }
  }

  return null // No DB entry and no config.json file found
})

// Update a JS script's configuration in the database
ipcMain.handle('db:update-js-script-config', async (event, { scriptName, values, schema }) => {
  if (schema) {
    // 如果提供了schema，同时更新schema和values
    return await db.run(
      'UPDATE js_script_configs SET config_schema = ?, config_values = ? WHERE script_name = ?',
      JSON.stringify(schema),
      JSON.stringify(values),
      scriptName
    )
  } else {
    // 只更新values
    return await db.run(
      'UPDATE js_script_configs SET config_values = ? WHERE script_name = ?',
      JSON.stringify(values),
      scriptName
    )
  }
})

// Add a new JS script (creates directory and DB entry)
ipcMain.handle('fs:add-js-script', async (event, name) => {
  try {
    const newScriptPath = join(jsScriptsPath, name)
    if (fs.existsSync(newScriptPath)) {
      throw new Error(`脚本目录 '${name}' 已存在。`)
    }
    fs.mkdirSync(newScriptPath, { recursive: true })

    // Create a default main.js file
    const defaultContent = `// Script for ${name}
const fs = require('fs');
const path = require('path');

// Get configuration from command line arguments
let config = {};
if (process.argv[2]) {
  try {
    const configString = Buffer.from(process.argv[2], 'base64').toString('utf-8');
    config = JSON.parse(configString);
    console.log('--- Running script ${name} ---');
    console.log('Received configuration:');
    console.log(JSON.stringify(config, null, 2));
    console.log('');
  } catch (e) {
    console.error('Failed to parse configuration:', e.message);
    process.exit(1);
  }
} else {
  console.log('--- Running script ${name} ---');
  console.log('No configuration provided');
  console.log('');
}

// Your script logic here
console.log('Current time:', new Date().toLocaleString());

if (config.enable_logging) {
  console.log('Logging is enabled.');
}

console.log('');
console.log('--- Script finished ---');
`;
    fs.writeFileSync(join(newScriptPath, 'main.js'), defaultContent);

    // For a new script, we start with an empty configuration, same as Python scripts.
    const emptySchema = []
    const emptyValues = {}

    // Create an empty config.json as a placeholder and for consistency.
    fs.writeFileSync(join(newScriptPath, 'config.json'), JSON.stringify(emptySchema, null, 2));

    // Add entry to the database with an empty config
    await db.run(
      'INSERT OR IGNORE INTO js_script_configs (script_name, config_schema, config_values) VALUES (?, ?, ?)',
      name,
      JSON.stringify(emptySchema),
      JSON.stringify(emptyValues)
    )

    return { name, path: newScriptPath }
  } catch (error) {
    throw { message: error.message }
  }
})

// Delete a JS script (deletes directory and DB entry)
ipcMain.handle('fs:delete-js-script', async (event, name) => {
  try {
    const scriptPath = join(jsScriptsPath, name)
    if (!fs.existsSync(scriptPath)) {
      throw new Error(`脚本目录 '${name}' 未找到。`)
    }
    fs.rmSync(scriptPath, { recursive: true, force: true })

    // Also delete from the database
    await db.run('DELETE FROM js_script_configs WHERE script_name = ?', name)

    return { success: true }
  } catch (error) {
    throw { message: error.message }
  }
})

// --- Script Execution ---

// Helper function to create safe serializable objects
function createSerializableObject(obj) {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string' || typeof obj === 'number' || typeof obj === 'boolean') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => createSerializableObject(item));
  }

  if (typeof obj === 'object') {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'function') {
        continue; // Skip functions
      }
      if (value instanceof Error) {
        result[key] = {
          name: value.name,
          message: value.message,
          stack: value.stack
        };
      } else if (Buffer.isBuffer(value)) {
        result[key] = value.toString();
      } else {
        result[key] = createSerializableObject(value);
      }
    }
    return result;
  }

  return String(obj); // Convert everything else to string
}

// Execute Python Script
ipcMain.handle('python:run-script', async (event, { scriptName, configValues }) => {
  console.log('=== Python Script IPC Handler Called ===')
  console.log('Received parameters:', { scriptName, configValues })

  return new Promise((resolve, reject) => {
    const scriptDirectory = join(pythonScriptsPath, scriptName)
    const scriptPath = join(scriptDirectory, 'main.py')

    // 基本调试信息（仅在开发模式下显示）
    if (process.env.NODE_ENV === 'development') {
      console.log('=== Python Script Execution Debug ===')
      console.log('Script Name:', scriptName)
      console.log('Script Directory:', scriptDirectory)
      console.log('Script Path Exists:', fs.existsSync(scriptPath))
    }

    if (!fs.existsSync(scriptPath)) {
      // Reject with a serializable object
      const error = createSerializableObject({
        name: 'FileNotFoundError',
        message: `脚本文件未找到: ${scriptPath}`,
        debug: {
          scriptName: scriptName,
          appPath: app.getAppPath(),
          scriptsRoot: scriptsRoot,
          pythonScriptsPath: pythonScriptsPath,
          scriptDirectory: scriptDirectory,
          scriptPath: scriptPath
        }
      });
      console.error('FileNotFoundError:', error);
      return reject(error);
    }

    const configJsonString = JSON.stringify(configValues)
    const encodedConfig = Buffer.from(configJsonString).toString('base64')

    // 尝试多个Python命令
    const pythonCommands = ['python', 'python3', 'py']
    let pythonCmd = 'python'

    // 检查哪个Python命令可用
    for (const cmd of pythonCommands) {
      try {
        const { execSync } = require('child_process')
        execSync(`${cmd} --version`, { stdio: 'ignore' })
        pythonCmd = cmd
            if (process.env.NODE_ENV === 'development') {
          console.log('Using Python command:', pythonCmd)
        }
        break
      } catch (e) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`Python command '${cmd}' not available`)
        }
      }
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('Executing command:', pythonCmd, [scriptPath, encodedConfig])
      console.log('Working directory:', scriptDirectory)
    }

    const pythonProcess = spawn(pythonCmd, [scriptPath, encodedConfig], {
      cwd: scriptDirectory,
      stdio: ['pipe', 'pipe', 'pipe']
    })

    let stdout = ''
    let stderr = ''

    pythonProcess.stdout.on('data', (data) => {
      const output = data.toString()
      if (process.env.NODE_ENV === 'development') {
        console.log('Python stdout:', output)
      }
      stdout += output
    })

    pythonProcess.stderr.on('data', (data) => {
      const output = data.toString()
      if (process.env.NODE_ENV === 'development') {
        console.log('Python stderr:', output)
      }
      stderr += output
    })

    pythonProcess.on('close', (code) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Python process closed with code:', code)
        console.log('Final stdout:', stdout)
        console.log('Final stderr:', stderr)
      }

      if (code === 0) {
        const result = createSerializableObject({
          success: true,
          stdout: stdout || '',
          stderr: stderr || ''
        });
        // console.log('Python script executed successfully:', result); // 移除调试日志避免前端显示
        resolve(result);
      } else {
        console.error(`Python script [${scriptName}] exited with code ${code}. Stderr: ${stderr}`)
        // Reject with a serializable object
        const error = createSerializableObject({
          name: 'PythonScriptError',
          message: `脚本执行失败，退出码: ${code}`,
          stdout: stdout || '',
          stderr: stderr || '',
          exitCode: code
        });
        console.error('PythonScriptError:', error);
        reject(error);
      }
    })

    pythonProcess.on('error', (err) => {
      // This handles errors like `python` command not found
      console.error(`Failed to start subprocess for [${scriptName}]:`, err)
      // Reject with a serializable object
      const error = createSerializableObject({
        name: 'SpawnError',
        message: `无法启动Python子进程。请确保 'python' 命令在系统的 PATH 中可用。错误: ${err.message}`,
        originalError: err.message || '',
        pythonCmd: pythonCmd,
        debug: {
          scriptName: scriptName,
          scriptPath: scriptPath,
          scriptDirectory: scriptDirectory,
          pythonCmd: pythonCmd
        }
      });
      console.error('SpawnError:', error);
      reject(error);
    })
  })
})

// Execute CMD Script
ipcMain.handle('cmd:run-script', async (event, { id, content }) => {
  const tempDir = path.join(app.getPath('temp'), 'qingfeng_scripts')
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir)
  }
  const tempFilePath = path.join(tempDir, `${id}_${Date.now()}.cmd`)
  
  // Convert content to GBK buffer for CMD compatibility
  const gbkBuffer = iconv.encode(content, 'gbk')
  fs.writeFileSync(tempFilePath, gbkBuffer)

  const child = exec(`start cmd.exe /K "${tempFilePath}"`, { shell: true })
  
  let output = ''
  child.stdout.on('data', (data) => {
    output += data.toString()
  })
  child.stderr.on('data', (data) => {
    output += data.toString()
  })

  return new Promise((resolve) => {
    child.on('close', async (code) => {
      const logContent = `Script executed with exit code ${code}.\nOutput:\n${output}`
      await db.run('INSERT INTO script_logs (script_id, log_content) VALUES (?, ?)', id, logContent)

      // Clean up temp file with error handling
      try {
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath)
        }
      } catch (error) {
        console.warn('Failed to delete temp file:', tempFilePath, error.message)
      }

      resolve({ code, output })
    })
  })
})

ipcMain.handle('db:get-script-logs', async (event, scriptId) => {
  return await db.all('SELECT * FROM script_logs WHERE script_id = ? ORDER BY timestamp DESC', scriptId)
})


function createWindow() {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1400,
    height: 1000,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alexeyboiko/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    if (optimizer && optimizer.watchWindowShortcuts) {
      optimizer.watchWindowShortcuts(window)
    }
  })

  // Create script directories on startup if they don't exist
  console.log('Creating script directories...')
  console.log('Scripts root:', scriptsRoot)
  console.log('Python scripts path:', pythonScriptsPath)
  console.log('JS scripts path:', jsScriptsPath)

  if (!fs.existsSync(scriptsRoot)) {
    console.log('Creating scripts root directory')
    fs.mkdirSync(scriptsRoot, { recursive: true })
  }
  if (!fs.existsSync(pythonScriptsPath)) {
    console.log('Creating python scripts directory')
    fs.mkdirSync(pythonScriptsPath, { recursive: true })
  }
  if (!fs.existsSync(jsScriptsPath)) {
    console.log('Creating js scripts directory')
    fs.mkdirSync(jsScriptsPath, { recursive: true })
  }

  await initializeDatabase()

  // IPC test handler
  ipcMain.handle('ping', () => 'pong')

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Execute JS Script
ipcMain.handle('js:run-script', async (event, { scriptName, configValues }) => {
  console.log('=== JS Script IPC Handler Called ===')
  console.log('Received parameters:', { scriptName, configValues })
  console.log('Parameter types:', {
    scriptName: typeof scriptName,
    configValues: typeof configValues
  })

  try {
    return await new Promise((resolve, reject) => {
    const scriptDirectory = join(jsScriptsPath, scriptName)
    const scriptPath = join(scriptDirectory, 'main.js')

    // 基本调试信息（仅在开发模式下显示）
    if (process.env.NODE_ENV === 'development') {
      console.log('=== JS Script Execution Debug ===')
      console.log('Script Name:', scriptName)
      console.log('Script Directory:', scriptDirectory)
      console.log('Script Path Exists:', fs.existsSync(scriptPath))
    }

    if (!fs.existsSync(scriptPath)) {
      const error = new Error(`脚本文件 '${scriptPath}' 未找到。`)
      console.error('Script file not found:', error.message)
      reject(createSerializableObject({
        message: error.message,
        stdout: '',
        stderr: `Error: ${error.message}`,
        debug: {
          scriptPath,
          exists: false
        }
      }))
      return
    }

    // Encode configuration as base64 to pass as command line argument
    const encodedConfig = Buffer.from(JSON.stringify(configValues || {})).toString('base64')

    // Try different Node.js commands
    const nodeCommands = ['node', 'nodejs']
    let nodeCmd = null

    for (const cmd of nodeCommands) {
      try {
        require('child_process').execSync(`${cmd} --version`, { stdio: 'ignore' })
        nodeCmd = cmd
        if (process.env.NODE_ENV === 'development') {
          console.log('Using Node command:', nodeCmd)
        }
        break
      } catch (e) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`Node command '${cmd}' not available`)
        }
      }
    }

    if (!nodeCmd) {
      const error = new Error('Node.js 未安装或不在系统 PATH 中。请安装 Node.js。')
      console.error('Node.js not found:', error.message)
      reject(createSerializableObject({
        message: error.message,
        stdout: '',
        stderr: `Error: ${error.message}`,
        debug: {
          nodeCommands,
          available: false
        }
      }))
      return
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('Executing command:', nodeCmd, [scriptPath, encodedConfig])
      console.log('Working directory:', scriptDirectory)
    }

    const { spawn } = require('child_process')
    const nodeProcess = spawn(nodeCmd, [scriptPath, encodedConfig], {
      cwd: scriptDirectory,
      stdio: ['pipe', 'pipe', 'pipe']
    })

    let stdout = ''
    let stderr = ''

    nodeProcess.stdout.on('data', (data) => {
      const output = data.toString()
      if (process.env.NODE_ENV === 'development') {
        console.log('Node stdout:', output)
      }
      stdout += output
    })

    nodeProcess.stderr.on('data', (data) => {
      const output = data.toString()
      if (process.env.NODE_ENV === 'development') {
        console.log('Node stderr:', output)
      }
      stderr += output
    })

    nodeProcess.on('close', (code) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Node process closed with code:', code)
        console.log('Final stdout:', stdout)
        console.log('Final stderr:', stderr)
      }

      const result = createSerializableObject({
        success: code === 0,
        stdout: stdout || '',
        stderr: stderr || ''
      })

      if (code === 0) {
        // console.log('JS script executed successfully:', result); // 移除调试日志避免前端显示
        resolve(result)
      } else {
        console.error('JS script execution failed with code:', code)
        reject(createSerializableObject({
          message: `脚本执行失败，退出代码: ${code}`,
          stdout: stdout || '',
          stderr: stderr || '',
          debug: {
            exitCode: code,
            command: `${nodeCmd} ${scriptPath} ${encodedConfig}`,
            workingDirectory: scriptDirectory
          }
        }))
      }
    })

    nodeProcess.on('error', (error) => {
      console.error('Node process error:', error)
      reject(createSerializableObject({
        message: `进程启动失败: ${error.message}`,
        stdout: stdout || '',
        stderr: stderr || '',
        originalError: error.message,
        debug: {
          error: error.message,
          command: `${nodeCmd} ${scriptPath} ${encodedConfig}`,
          workingDirectory: scriptDirectory
        }
      }))
    })
    })
  } catch (error) {
    console.error('JS Script execution error:', error)
    throw createSerializableObject({
      message: `脚本执行异常: ${error.message}`,
      stdout: '',
      stderr: error.message || '',
      originalError: error.message,
      debug: {
        error: error.message,
        stack: error.stack
      }
    })
  }
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
