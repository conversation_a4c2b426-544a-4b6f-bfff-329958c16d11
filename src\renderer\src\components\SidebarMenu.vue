<template>
  <div class="sidebar-menu">
    <ul>
      <li
        v-for="item in menuItems"
        :key="item.name"
        :class="{ active: activeMenu === item.name }"
        @click="activateMenu(item.name)"
      >
        <span class="icon">{{ item.icon }}</span>
        <span class="text">{{ item.label }}</span>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const emit = defineEmits(['menu-click'])

const activeMenu = ref('CmdScripts')

const menuItems = computed(() => [
  { name: 'Workbench', label: '工作台', icon: '💼' },
  { name: 'CmdScripts', label: 'CMD脚本', icon: '📜' },
  { name: 'JsScripts', label: 'JS脚本', icon: '📜' },
  { name: 'PythonScripts', label: 'Python脚本', icon: '🐍' }
])

function activateMenu(name) {
  activeMenu.value = name
  emit('menu-click', name)
}

// Default emit
emit('menu-click', activeMenu.value)
</script>

<style scoped>
.sidebar-menu {
  width: 220px;
  background-color: #f0f2f5;
  padding: 20px 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e0e0e0;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

li {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  border-radius: 8px;
  margin-bottom: 8px;
  font-size: 15px;
  color: #333;
  transition:
    background-color 0.2s ease,
    color 0.2s ease;
}

li:hover {
  background-color: #e6eaf0;
}

li.active {
  background-color: #4a90e2;
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.icon {
  margin-right: 15px;
  font-size: 18px;
  width: 20px; /* Ensure icons are aligned */
  text-align: center;
}

.text {
  line-height: 1.2;
}
</style> 