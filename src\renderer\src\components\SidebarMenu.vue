<template>
  <div class="sidebar-menu">
    <div class="menu-content">
      <ul>
        <li
          v-for="item in menuItems"
          :key="item.name"
          :class="{ active: activeMenu === item.name }"
          @click="activateMenu(item.name)"
        >
          <span class="icon">{{ item.icon }}</span>
          <span class="text">{{ item.label }}</span>
        </li>
      </ul>
    </div>

    <div class="menu-footer">
      <div class="reload-section">
        <button @click="reloadApp" class="reload-btn" title="热重载项目">
          <span class="reload-icon">🔄</span>
          <span class="reload-text">热重载</span>
        </button>
      </div>

      <div class="info-section">
        <div class="version-info">
          <span class="version-text">v1.0.0</span>
        </div>
        <div class="time-info">
          <span class="time-text">{{ currentTime }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const emit = defineEmits(['menu-click'])

const activeMenu = ref('CmdScripts')
const currentTime = ref('')

const menuItems = computed(() => [
  { name: 'Workbench', label: '工作台', icon: '💼' },
  { name: 'CmdScripts', label: 'CMD脚本', icon: '📜' },
  { name: 'JsScripts', label: 'JS脚本', icon: '📄' },
  { name: 'PythonScripts', label: 'Python脚本', icon: '🐍' },
  { name: 'Hotkeys', label: '快捷键', icon: '⌨️' },
  { name: 'Settings', label: '设置', icon: '⚙️' }
])

function activateMenu(name) {
  activeMenu.value = name
  emit('menu-click', name)
}

function reloadApp() {
  if (window.location) {
    window.location.reload()
  }
}

function updateTime() {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  })
}

let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})

// Default emit
emit('menu-click', activeMenu.value)
</script>

<style scoped>
.sidebar-menu {
  width: 220px;
  background-color: #1a1a1a;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #333;
}

.menu-content {
  flex: 1;
  padding: 20px 10px;
  overflow-y: auto;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

li {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  border-radius: 8px;
  margin-bottom: 8px;
  font-size: 15px;
  color: #e0e0e0;
  transition:
    background-color 0.2s ease,
    color 0.2s ease;
}

li:hover {
  background-color: #2c2c2c;
}

li.active {
  background-color: #4a90e2;
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.3);
}

.icon {
  margin-right: 15px;
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.text {
  line-height: 1.2;
}

.menu-footer {
  padding: 15px 10px;
  border-top: 1px solid #333;
  background-color: #161616;
}

.reload-section {
  margin-bottom: 12px;
}

.reload-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  background-color: #2d3748;
  color: #e0e0e0;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

.reload-btn:hover {
  background-color: #4a5568;
}

.reload-icon {
  margin-right: 6px;
  font-size: 14px;
}

.reload-text {
  font-weight: 500;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.version-info,
.time-info {
  display: flex;
  justify-content: center;
  align-items: center;
}

.version-text,
.time-text {
  font-size: 11px;
  color: #888;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.version-text {
  font-weight: 500;
}

.time-text {
  font-weight: 400;
}
</style>