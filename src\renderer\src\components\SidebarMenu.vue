<template>
  <div class="sidebar-menu">
    <div class="menu-header">
      <div class="logo-section">
        <img src="../assets/logo.ico" alt="Logo" class="logo" />
        <span class="app-title">清风系统</span>
      </div>
    </div>

    <div class="menu-content">
      <ul>
        <li
          v-for="item in menuItems"
          :key="item.name"
          :class="{
            active: props.activeMenu === item.name || (item.children && item.children.some(child => child.name === props.activeMenu)),
            'has-children': item.children,
            expanded: item.children && expandedMenus.includes(item.name)
          }"
          @click="handleMenuClick(item)"
        >
          <div class="menu-item-content">
            <span class="icon">{{ item.icon }}</span>
            <span class="text">{{ item.label }}</span>
            <span v-if="item.children" class="expand-icon">
              {{ expandedMenus.includes(item.name) ? '▼' : '▶' }}
            </span>
          </div>

          <!-- 二级菜单 -->
          <ul v-if="item.children && expandedMenus.includes(item.name)" class="submenu">
            <li
              v-for="child in item.children"
              :key="child.name"
              :class="{ active: props.activeMenu === child.name }"
              @click.stop="activateMenu(child.name)"
            >
              <span class="icon">{{ child.icon }}</span>
              <span class="text">{{ child.label }}</span>
            </li>
          </ul>
        </li>
      </ul>
    </div>

    <div class="menu-footer">
      <div class="reload-section">
        <button @click="reloadApp" class="reload-btn" title="热重载项目">
          <span class="reload-icon">🔄</span>
          <span class="reload-text">热重载</span>
        </button>
      </div>

      <div class="info-section">
        <div class="info-card">
          <div class="info-row">
            <span class="version-info">v1.0.0</span>
            <span class="time-info">{{ currentTime }}</span>
          </div>
          <div class="date-info">{{ currentDate }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  activeMenu: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['menu-click'])

const currentTime = ref('')
const currentDate = ref('')
const expandedMenus = ref(['ScriptManagement']) // 默认展开脚本管理

const menuItems = computed(() => [
  { name: 'Workbench', label: '工作台', icon: '💼' },
  {
    name: 'ScriptManagement',
    label: '脚本管理',
    icon: '📁',
    children: [
      { name: 'CmdScripts', label: 'CMD脚本', icon: '📜' },
      { name: 'JsScripts', label: 'JS脚本', icon: '📄' },
      { name: 'PythonScripts', label: 'Python脚本', icon: '🐍' }
    ]
  },
  { name: 'ScheduledTasks', label: '定时任务', icon: '⏰' },
  { name: 'Hotkeys', label: '快捷键', icon: '⌨️' },
  { name: 'Settings', label: '设置', icon: '⚙️' }
])

function handleMenuClick(item) {
  if (item.children) {
    // 切换展开/收起状态
    const index = expandedMenus.value.indexOf(item.name)
    if (index > -1) {
      expandedMenus.value.splice(index, 1)
    } else {
      expandedMenus.value.push(item.name)
    }
  } else {
    activateMenu(item.name)
  }
}

function activateMenu(name) {
  emit('menu-click', name)
}

function reloadApp() {
  if (window.api && typeof window.api.relaunchApp === 'function') {
    window.api.relaunchApp()
  } else {
    console.error('Relaunch API not available.')
  }
}

function updateTime() {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})

// Default emit
// emit('menu-click', activeMenu.value) --- REMOVED
</script>

<style scoped>
.sidebar-menu {
  width: 240px;
  background-color: #1a1a1a;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #333;
}

.menu-header {
  padding: 16px;
  border-bottom: 1px solid #333;
  background-color: #161616;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.app-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5px;
}

.menu-content {
  flex: 1;
  padding: 16px 10px;
  overflow-y: auto;
}

.menu-content::-webkit-scrollbar {
  width: 4px;
}

.menu-content::-webkit-scrollbar-track {
  background: transparent;
}

.menu-content::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 2px;
}

.menu-content::-webkit-scrollbar-thumb:hover {
  background: #444;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

li {
  margin-bottom: 4px;
  font-size: 15px;
  color: #e0e0e0;
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease, color 0.2s ease;
}

li:hover .menu-item-content {
  background-color: #2c2c2c;
}

li.active .menu-item-content {
  background-color: #4a90e2;
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.3);
}

li.has-children.expanded .menu-item-content {
  background-color: #2a2a2a;
}

.icon {
  margin-right: 12px;
  font-size: 16px;
  width: 18px;
  text-align: center;
}

.text {
  flex: 1;
  line-height: 1.2;
}

.expand-icon {
  font-size: 12px;
  color: #888;
  transition: transform 0.2s ease;
}

/* 二级菜单样式 */
.submenu {
  margin-top: 4px;
  margin-left: 20px;
  border-left: 2px solid #333;
  padding-left: 0;
}

.submenu li {
  margin-bottom: 2px;
}

.submenu li {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  cursor: pointer;
  border-radius: 6px;
  font-size: 14px;
  color: #b0b0b0;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.submenu li:hover {
  background-color: #2c2c2c;
  color: #e0e0e0;
}

.submenu li.active {
  background-color: #4a90e2;
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(74, 144, 226, 0.3);
}

.submenu .icon {
  margin-right: 10px;
  font-size: 14px;
  width: 16px;
}

.menu-footer {
  padding: 15px 10px;
  border-top: 1px solid #333;
  background-color: #161616;
}

.reload-section {
  margin-bottom: 15px;
}

.reload-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 12px;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  color: #e0e0e0;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.reload-btn:hover {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.reload-icon {
  margin-right: 6px;
  font-size: 14px;
}

.info-section {
  display: flex;
  flex-direction: column;
}

.info-card {
  background: linear-gradient(135deg, #252525 0%, #1f1f1f 100%);
  border-radius: 8px;
  padding: 10px 12px;
  border: 1px solid #333;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.version-info {
  font-size: 12px;
  color: #4a90e2;
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.time-info {
  font-size: 12px;
  color: #e0e0e0;
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.date-info {
  font-size: 10px;
  color: #888;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-align: center;
}
</style>