<template>
  <div class="cmd-scripts-container">
    <div class="script-list-panel">
      <div class="panel-header">
        <h3>CMD 脚本列表</h3>
        <button @click="openAddModal" class="add-btn">新增脚本</button>
      </div>
      <ul> 
        <li
          v-for="script in scripts"
          :key="script.id"
          @click="selectScript(script)"
          :class="{ active: selectedScript && selectedScript.id === script.id }"
        >
          <span>{{ script.name }}</span>
          <button @click.stop="deleteScript(script)" class="delete-btn">删除</button>
        </li> 
      </ul>
    </div>
    <div class="script-editor-panel" v-if="selectedScript">
      <div class="editor-header">
        <input type="text" v-model="selectedScript.name" class="title-input" readonly />
        <div class="button-group"> 
          <button @click="runScript" class="run-btn">运行</button>
          <button @click="saveScript" :disabled="!isDirty" class="save-btn">
            {{ isDirty ? '保存' : '已保存' }}
          </button>
        </div>
      </div>
      <p class="script-description">{{ selectedScript.description }}</p>
      <textarea v-model="editedContent" class="code-editor"></textarea> 
    </div>
    <div v-else class="placeholder">
      <p>请从左侧选择一个脚本进行编辑，或新增一个脚本。</p>
    </div>

    <!-- Add Script Modal -->
    <div v-if="showAddModal" class="modal-overlay" @click.self="closeAddModal">
      <div class="modal-content">
        <h3>新增 CMD 脚本</h3>
        <input type="text" v-model="newScriptName" placeholder="脚本名称" />
        <textarea v-model="newScriptDescription" placeholder="脚本描述"></textarea>
        <div class="modal-actions">
          <button @click="closeAddModal">取消</button>
          <button @click="addScript">确认</button>
        </div>
      </div>
    </div>

    <!-- Logs Modal -->
    <div v-if="showLogsModal" class="modal-overlay" @click.self="closeLogsModal">
      <div class="modal-content logs-modal">
        <h3>“{{ selectedScript.name }}” 的运行日志</h3>
        <div v-if="logs.length" class="logs-container">
          <ul>
            <li v-for="log in logs" :key="log.id">
              <p><strong>时间:</strong> {{ new Date(log.timestamp).toLocaleString() }}</p>
              <pre>{{ log.log_content }}</pre>
            </li>
          </ul>
        </div>
        <div v-else>
          <p>暂无日志记录。</p>
        </div>
        <div class="modal-actions">
          <button @click="closeLogsModal">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'

const scripts = ref([])
const selectedScript = ref(null)
const editedContent = ref('')
const isDirty = ref(false)

const showAddModal = ref(false)
const newScriptName = ref('')
const newScriptDescription = ref('')

const showLogsModal = ref(false)
const logs = ref([])

async function fetchScripts() {
  scripts.value = await window.api.getCmdScripts()
}

onMounted(fetchScripts)

watch(selectedScript, (newVal) => {
  if (newVal) {
    editedContent.value = newVal.content
    isDirty.value = false
  } else {
    editedContent.value = ''
  }
})

watch(editedContent, (newVal) => {
  if (selectedScript.value) {
    isDirty.value = newVal !== selectedScript.value.content
  }
})

function selectScript(script) {
  if (isDirty.value) {
    if (!confirm('当前脚本有未保存的更改，确定要切换吗？')) {
      return
    }
  }
  selectedScript.value = script
}

async function saveScript() {
  if (!selectedScript.value || !isDirty.value) return
  await window.api.updateCmdScript({
    id: selectedScript.value.id,
    content: editedContent.value
  })
  selectedScript.value.content = editedContent.value
  isDirty.value = false
}

function openAddModal() {
  showAddModal.value = true
}

function closeAddModal() {
  showAddModal.value = false
  newScriptName.value = ''
  newScriptDescription.value = ''
}

async function addScript() {
  if (!newScriptName.value.trim()) {
    alert('脚本名称不能为空')
    return
  }
  try {
    const newScript = await window.api.addCmdScript({
      name: newScriptName.value,
      description: newScriptDescription.value
    })

    if (newScript && newScript.id) {
      await fetchScripts()
      const scriptInList = scripts.value.find((s) => s.id === newScript.id)
      if (scriptInList) {
        selectScript(scriptInList)
      }
      closeAddModal()
    } else {
      alert('添加脚本失败：从后端未收到有效的新脚本信息。')
      console.error('Failed to add script, invalid response:', newScript)
    }
  } catch (error) {
    console.error('添加脚本时出错:', error)
    alert(`添加脚本时出错: ${error.message}\n请检查开发者工具(Ctrl+Shift+I)中的控制台以获取详细信息。`)
  }
}

async function deleteScript(script) {
  if (confirm(`确定要删除脚本 “${script.name}” 吗？`)) {
    await window.api.deleteCmdScript(script.id)
    if (selectedScript.value && selectedScript.value.id === script.id) {
      selectedScript.value = null
    }
    await fetchScripts()
  }
}

async function runScript() {
  if (!selectedScript.value) return

  // 1. Auto-save if dirty, without confirmation
  if (isDirty.value) {
    await saveScript()
    // After saving, give a brief feedback to the user.
    // This is optional, but good for UX.
    console.log('Script auto-saved before running.')
  }

  // 2. Directly run the script without alert
  await window.api.runCmdScript({
    id: selectedScript.value.id,
    content: selectedScript.value.content
  })
}

async function showLogs() {
  if (!selectedScript.value) return
  logs.value = await window.api.getScriptLogs(selectedScript.value.id)
  showLogsModal.value = true
}

function closeLogsModal() {
  showLogsModal.value = false
  logs.value = []
}
</script>

<style scoped>
.cmd-scripts-container {
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: #1a1a1a;
  color: #fff;
  overflow: hidden;
}

.script-list-panel {
  width: 280px;
  border-right: 1px solid #333;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 1rem;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
}

.add-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}
.add-btn:hover {
  background-color: #45a049;
}

.script-list-panel ul {
  list-style: none;
  padding: 0.5rem;
  margin: 0;
  overflow-y: auto;
  flex-grow: 1;
}

.script-list-panel li {
  padding: 0.8rem 1rem;
  margin-bottom: 0.3rem;
  cursor: pointer;
  border-radius: 6px; /* Added border-radius */
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease, color 0.2s ease; /* Smooth transition */
}

.script-list-panel li:hover {
  background-color: #2c2c2c;
}

.script-list-panel li.active {
  background-color: #0056b3; /* A deeper, more prominent blue */
  color: white;
  font-weight: 500;
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.5); /* Subtle glow effect */
}

.delete-btn {
  background-color: transparent;
  color: #ff5555;
  border: none;
  cursor: pointer;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s;
}

.script-list-panel li:hover .delete-btn {
  visibility: visible;
  opacity: 1;
}


.script-editor-panel {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.title-input {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

.button-group button {
  margin-left: 0.5rem;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}
.log-btn {
  background-color: #3f51b5;
  color: white;
}
.run-btn {
  background-color: #ff9800;
  color: white;
}
.save-btn {
  background-color: #007bff;
  color: white;
}
.save-btn:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.script-description {
  color: #aaa;
  margin-bottom: 1rem;
  font-style: italic;
}

.code-editor {
  flex-grow: 1;
  background-color: #252525;
  color: #f0f0f0;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 1rem;
  font-family: 'Courier New', Courier, monospace;
  font-size: 1rem;
  resize: none;
}

.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
  color: #888;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: #2c2c2c;
  padding: 2rem;
  border-radius: 8px;
  width: 400px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-content h3 {
  margin-top: 0;
}

.modal-content input,
.modal-content textarea {
  width: 100%;
  padding: 0.5rem;
  background-color: #333;
  border: 1px solid #555;
  color: white;
  border-radius: 4px;
}

.modal-content textarea {
  min-height: 100px;
  resize: vertical;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}
.modal-actions button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}
.modal-actions button:last-child {
  background-color: #007bff;
  color: white;
}

.logs-modal {
  width: 70%;
  max-width: 800px;
  height: 70%;
}

.logs-container {
  flex-grow: 1;
  overflow-y: auto;
  background-color: #1e1e1e;
  padding: 1rem;
  border-radius: 4px;
}
.logs-container ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.logs-container li {
  border-bottom: 1px solid #444;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}
.logs-container pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #252525;
  padding: 0.5rem;
  border-radius: 4px;
  color: #ddd;
}
</style> 