import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

const api = {
  getCmdScripts: () => ipcRenderer.invoke('db:get-cmd-scripts'),
  addCmdScript: (data) => ipcRenderer.invoke('db:add-cmd-script', data),
  updateCmdScript: (data) => ipcRenderer.invoke('db:update-cmd-script', data),
  deleteCmdScript: (id) => ipcRenderer.invoke('db:delete-cmd-script', id),
  runCmdScript: (script) => ipcRenderer.invoke('cmd:run-script', script),
  getScriptLogs: (scriptId) => ipcRenderer.invoke('db:get-script-logs', scriptId),

  // Python Scripts
  getPythonScripts: () => ipcRenderer.invoke('python:get-scripts'),
  getPythonScriptConfig: (scriptName) => ipcRenderer.invoke('python:get-script-config', scriptName),
  updatePythonScriptConfig: (config) => ipcRenderer.invoke('db:update-python-script-config', config),
  addPythonScript: (name) => ipcRenderer.invoke('python:add-script', name),
  deletePythonScript: (name) => ipcRenderer.invoke('python:delete-script', name),
  savePythonScriptConfig: (scriptName, configValues) => ipcRenderer.invoke('python:save-script-config', scriptName, configValues),
  runPythonScript: (scriptName) => ipcRenderer.invoke('python:run-script', scriptName),
  testPythonEnvironment: () => ipcRenderer.invoke('test:python-environment'),

  // JS Scripts
  getJsScripts: () => ipcRenderer.invoke('fs:get-js-scripts'),
  getJsScriptConfig: (scriptName) => ipcRenderer.invoke('db:get-js-script-config', scriptName),
  updateJsScriptConfig: (config) => ipcRenderer.invoke('db:update-js-script-config', config),
  addJsScript: (name) => ipcRenderer.invoke('fs:add-js-script', name),
  deleteJsScript: (name) => ipcRenderer.invoke('fs:delete-js-script', name),
  runJsScript: (args) => ipcRenderer.invoke('js:run-script', args),
  testNodeEnvironment: () => ipcRenderer.invoke('test:node-environment'),
  relaunchApp: () => ipcRenderer.send('relaunch-app')
}

if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  window.electron = electronAPI
  window.api = api
}
