<script setup>
import { ref, markRaw } from 'vue'
import SidebarMenu from './components/SidebarMenu.vue'
import CmdScripts from './components/CmdScripts.vue'
import JsScripts from './components/JsScripts.vue'
import PythonScripts from './components/PythonScripts.vue'
import Workbench from './components/Workbench.vue'
import HotkeySettings from './components/HotkeySettings.vue'
import Settings from './components/Settings.vue'
import ScheduledTasks from './components/ScheduledTasks.vue'

const components = {
  CmdScripts: markRaw(CmdScripts),
  JsScripts: markRaw(JsScripts),
  PythonScripts: markRaw(PythonScripts),
  Workbench: markRaw(Workbench),
  ScheduledTasks: markRaw(ScheduledTasks),
  Hotkeys: markRaw(HotkeySettings),
  Settings: markRaw(Settings)
}

const activeComponent = ref(components.CmdScripts)

function handleMenuClick(componentName) {
  if (components[componentName]) {
    activeComponent.value = components[componentName]
  }
}
</script>

<template>
  <div class="app-container">
    <SidebarMenu @menu-click="handleMenuClick" />
    <main class="main-content">
      <component :is="activeComponent" />
    </main>
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  background-color: #1a1a1a;
}

.main-content {
  flex-grow: 1;
  overflow: auto;
  padding: 0;
  background-color: #1a1a1a;
}
</style>
